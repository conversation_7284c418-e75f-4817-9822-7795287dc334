from main import airtanker_app
from flask import render_template, request, jsonify, session, flash, redirect, url_for
from endpoints.decorators import requires_authentication, requires_ldap_group_authentication
import os
from dotenv import load_dotenv, set_key, find_dotenv
import logging
from functools import wraps

# Load environment variables
load_dotenv()

"""
Settings Module for AirTanker Application

SIMPLIFIED ADMIN ACCESS SOLUTION:
Single route with multiple access methods for maximum flexibility.

ROUTE: /admin/settings

ACCESS METHODS:
1. Normal Access: /admin/settings
   - Requires user authentication (session)
   - Requires LDAP group membership ('finances')
   - Blocked by maintenance mode

2. URL Parameter Bypass: /admin/settings?admin=1 or /admin/settings?debug=1
   - NO authentication required
   - Works during maintenance mode
   - Bypasses all security checks
   - Logged with IP address for security

3. Emergency API: POST /emergency/disable_maintenance
   - NO authentication required
   - Requires emergency key from environment
   - Only disables maintenance mode

EMERGENCY ACCESS (when completely locked out):
- Navigate to: /admin/settings?admin=1
- Or use: /admin/settings?debug=1
- Or use emergency API with key

SECURITY:
- URL parameter access is logged with IP addresses
- Emergency API requires secret key from environment variables
- All administrative actions are logged
"""

def requires_authentication_bypass_maintenance(func):
    """
    Custom decorator that bypasses maintenance mode for critical admin functions.
    Still requires user authentication and LDAP group authentication.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Check if user is authenticated
        if 'username' not in session:
            if request.method == 'GET':
                return redirect(url_for('login'))
            else:
                return jsonify({'success': False, 'error': 'Authentication required'}), 401

        # Check if user has admin privileges
        if 'member_of' not in session or session.get('member_of') != 'finances':
            if request.method == 'GET':
                flash("You are not authorized to view that page.")
                return redirect(url_for("home"))
            else:
                return jsonify({'success': False, 'error': 'Admin privileges required'}), 403

        try:
            return func(*args, **kwargs)
        except Exception as error:
            airtanker_app.logger.error(f'Error in {func.__name__}: {str(error)}')
            if request.method == 'GET':
                flash(f'Error: {str(error)}', 'error')
                return redirect(url_for("home"))
            else:
                return jsonify({'success': False, 'error': str(error)}), 500

    return wrapper

@airtanker_app.route('/admin/settings', methods=['GET', 'POST'])
def admin_settings():
    """
    Admin-only settings page with flexible access control.

    Access Methods:
    1. URL Parameters: ?admin=1 or ?debug=1 (bypasses authentication)
    2. LDAP Authentication: Requires 'finances' group membership
    3. Emergency Access: Works even during maintenance mode with URL params

    Examples:
    - /admin/settings?admin=1
    - /admin/settings?debug=1
    - /admin/settings (requires authentication)
    """
    # Check for URL parameter bypass
    admin_param = request.args.get('admin') == '1'
    debug_param = request.args.get('debug') == '1'

    if admin_param or debug_param:
        # URL parameter access - bypass all authentication
        airtanker_app.logger.info(f'Admin settings accessed via URL parameter from IP: {request.remote_addr}')
        return settings_page_handler(bypass_auth=True)
    else:
        # Normal authentication flow - check authentication
        if 'username' not in session:
            return redirect(url_for('login'))

        # Check if user has admin privileges
        if 'member_of' not in session or session.get('member_of') != 'finances':
            flash("You are not authorized to view that page.")
            return redirect(url_for("home"))

        return settings_page_handler(bypass_auth=False)

def settings_page_handler(bypass_auth=False):
    if request.method == 'POST':
        try:
            # Handle settings updates
            setting_key = request.form.get('setting_key')
            setting_value = request.form.get('setting_value')

            if setting_key and setting_value is not None:
                # Find the .env file
                env_file = find_dotenv()
                if env_file:
                    # Update the environment variable
                    set_key(env_file, setting_key, setting_value)
                    flash(f'Setting {setting_key} updated successfully!', 'success')
                    user_info = "URL Parameter Access" if bypass_auth else session.get("name", "Unknown")
                    airtanker_app.logger.info(f'Setting {setting_key} updated by: {user_info}')
                else:
                    flash('Environment file not found!', 'error')
            else:
                flash('Invalid setting key or value!', 'error')

        except Exception as e:
            flash(f'Error updating setting: {str(e)}', 'error')
            airtanker_app.logger.error(f'Error updating setting: {str(e)}')

        return redirect(url_for('admin_settings'))

    # GET request - display settings
    try:
        # Get current environment settings
        settings_data = get_current_settings()

        # Get application configuration
        app_config = get_app_configuration()

        # Get system information
        system_info = get_system_information()

        return render_template('endpoints/settings.html',
                             settings=settings_data,
                             app_config=app_config,
                             system_info=system_info,
                             bypass_auth=bypass_auth)

    except Exception as e:
        flash(f'Error loading settings: {str(e)}', 'error')
        airtanker_app.logger.error(f'Error loading settings: {str(e)}')
        return render_template('endpoints/settings.html',
                             settings={},
                             app_config={},
                             system_info={},
                             bypass_auth=bypass_auth)

def get_current_settings():
    """Get current environment variable settings."""
    # Define which settings to display (excluding sensitive ones)
    safe_settings = [
        'PRODUCTION',
        'DETAILED_LOGGING',
        'ENABLE_NAME_MATCHING',
        'DEBUG_BYPASS_LOGIN',
        'LDAPADDRESS',
        'DB_SERVER',
        'DB_DATABASE',
        'ODOO_URL',
        'ODOO_DB',
        'PAYCOR_BASE_URL',
        'PAYCOR_ENTITY_ID'
    ]

    settings = {}
    for setting in safe_settings:
        value = os.getenv(setting, 'Not Set')
        # Mask sensitive information
        if 'PASSWORD' in setting.upper() or 'SECRET' in setting.upper() or 'TOKEN' in setting.upper() or 'KEY' in setting.upper():
            value = '***HIDDEN***' if value != 'Not Set' else 'Not Set'
        settings[setting] = value

    return settings

def get_app_configuration():
    """Get Flask application configuration."""
    config = {}

    # Safe configuration items to display
    safe_config_keys = [
        'MAINTENANCE_MODE',
        'ALLOWED_IPS',
        'MAX_CONTENT_LENGTH',
        'SESSION_PERMANENT',
        'PERMANENT_SESSION_LIFETIME',
        'SESSION_REFRESH_EACH_REQUEST',
        'WTF_CSRF_ENABLED'
    ]

    for key in safe_config_keys:
        if key in airtanker_app.config:
            config[key] = airtanker_app.config[key]

    return config

def get_system_information():
    """Get system and application information."""
    import platform
    import sys
    from datetime import datetime

    info = {
        'Python Version': sys.version,
        'Platform': platform.platform(),
        'Flask Version': getattr(airtanker_app, '__version__', 'Unknown'),
        'Current Time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'Debug Mode': airtanker_app.debug,
        'Environment': 'Production' if os.getenv('PRODUCTION', 'false').lower() == 'true' else 'Development'
    }

    return info

@airtanker_app.route('/api/settings/toggle_maintenance', methods=['POST'])
@requires_authentication_bypass_maintenance
def toggle_maintenance_mode():
    """API endpoint to toggle maintenance mode (requires authentication)."""
    try:
        current_mode = airtanker_app.config.get('MAINTENANCE_MODE', False)
        new_mode = not current_mode
        airtanker_app.config['MAINTENANCE_MODE'] = new_mode

        airtanker_app.logger.info(f'Maintenance mode {"enabled" if new_mode else "disabled"} by user {session.get("name", "Unknown")}')

        return jsonify({
            'success': True,
            'maintenance_mode': new_mode,
            'message': f'Maintenance mode {"enabled" if new_mode else "disabled"}'
        })
    except Exception as e:
        airtanker_app.logger.error(f'Error toggling maintenance mode: {str(e)}')
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@airtanker_app.route('/emergency/disable_maintenance', methods=['POST'])
def emergency_disable_maintenance():
    """
    Emergency endpoint to disable maintenance mode without authentication.
    Requires a special emergency key for security.

    Usage:
    POST /emergency/disable_maintenance
    Content-Type: application/json
    {
        "emergency_key": "your_emergency_key_here"
    }
    """
    try:
        # Get the emergency key from environment variables
        emergency_key = os.getenv('EMERGENCY_MAINTENANCE_KEY')

        if not emergency_key:
            airtanker_app.logger.warning('Emergency maintenance disable attempted but no emergency key is configured')
            return jsonify({
                'success': False,
                'error': 'Emergency key not configured'
            }), 500

        # Get the provided key from request
        data = request.get_json() or {}
        provided_key = data.get('emergency_key')

        if not provided_key:
            airtanker_app.logger.warning('Emergency maintenance disable attempted without providing key')
            return jsonify({
                'success': False,
                'error': 'Emergency key required'
            }), 400

        # Verify the emergency key
        if provided_key != emergency_key:
            airtanker_app.logger.warning('Emergency maintenance disable attempted with invalid key')
            return jsonify({
                'success': False,
                'error': 'Invalid emergency key'
            }), 403

        # Disable maintenance mode
        airtanker_app.config['MAINTENANCE_MODE'] = False
        airtanker_app.logger.info('Maintenance mode disabled via emergency endpoint')

        return jsonify({
            'success': True,
            'maintenance_mode': False,
            'message': 'Maintenance mode disabled via emergency endpoint'
        })

    except Exception as e:
        airtanker_app.logger.error(f'Error in emergency maintenance disable: {str(e)}')
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@airtanker_app.route('/emergency/disable_maintenance_form', methods=['GET'])
def emergency_disable_maintenance_form():
    """
    Emergency form page to disable maintenance mode.
    This page is accessible even when maintenance mode is enabled.
    """
    return render_template('emergency_disable.html')

@airtanker_app.route('/api/settings/system_status', methods=['GET'])
@requires_authentication
def get_system_status():
    """API endpoint to get current system status."""
    try:
        from services.DatabaseService import DatabaseService

        # Test database connection
        db_service = DatabaseService()
        db_connected = db_service.connect()
        if db_connected:
            db_service.disconnect()

        # Test Redis connection
        redis_connected = False
        try:
            from main import redis_client
            redis_client.ping()
            redis_connected = True
        except:
            redis_connected = False

        status = {
            'database': 'Connected' if db_connected else 'Disconnected',
            'redis': 'Connected' if redis_connected else 'Disconnected',
            'maintenance_mode': airtanker_app.config.get('MAINTENANCE_MODE', False),
            'environment': 'Production' if os.getenv('PRODUCTION', 'false').lower() == 'true' else 'Development'
        }

        return jsonify(status)

    except Exception as e:
        airtanker_app.logger.error(f'Error getting system status: {str(e)}')
        return jsonify({
            'error': str(e)
        }), 500