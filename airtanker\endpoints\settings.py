from main import airtanker_app
from flask import render_template, request, jsonify, session, flash, redirect, url_for
from endpoints.decorators import requires_authentication, requires_ldap_group_authentication
import os
from dotenv import load_dotenv, set_key, find_dotenv
import logging

# Load environment variables
load_dotenv()

@airtanker_app.route('/settings', methods=['GET', 'POST'])
@requires_authentication
@requires_ldap_group_authentication  # Only allow admin users
def settings():
    """
    Settings page for viewing and managing application configuration.
    Only accessible to users with proper LDAP group authentication.
    """
    if request.method == 'POST':
        try:
            # Handle settings updates
            setting_key = request.form.get('setting_key')
            setting_value = request.form.get('setting_value')

            if setting_key and setting_value is not None:
                # Find the .env file
                env_file = find_dotenv()
                if env_file:
                    # Update the environment variable
                    set_key(env_file, setting_key, setting_value)
                    flash(f'Setting {setting_key} updated successfully!', 'success')
                    airtanker_app.logger.info(f'Setting {setting_key} updated by user {session.get("name", "Unknown")}')
                else:
                    flash('Environment file not found!', 'error')
            else:
                flash('Invalid setting key or value!', 'error')

        except Exception as e:
            flash(f'Error updating setting: {str(e)}', 'error')
            airtanker_app.logger.error(f'Error updating setting: {str(e)}')

        return redirect(url_for('settings'))

    # GET request - display settings
    try:
        # Get current environment settings
        settings_data = get_current_settings()

        # Get application configuration
        app_config = get_app_configuration()

        # Get system information
        system_info = get_system_information()

        return render_template('endpoints/settings.html',
                             settings=settings_data,
                             app_config=app_config,
                             system_info=system_info)

    except Exception as e:
        flash(f'Error loading settings: {str(e)}', 'error')
        airtanker_app.logger.error(f'Error loading settings: {str(e)}')
        return render_template('endpoints/settings.html',
                             settings={},
                             app_config={},
                             system_info={})

def get_current_settings():
    """Get current environment variable settings."""
    # Define which settings to display (excluding sensitive ones)
    safe_settings = [
        'PRODUCTION',
        'DETAILED_LOGGING',
        'ENABLE_NAME_MATCHING',
        'DEBUG_BYPASS_LOGIN',
        'LDAPADDRESS',
        'DB_SERVER',
        'DB_DATABASE',
        'ODOO_URL',
        'ODOO_DB',
        'PAYCOR_BASE_URL',
        'PAYCOR_ENTITY_ID'
    ]

    settings = {}
    for setting in safe_settings:
        value = os.getenv(setting, 'Not Set')
        # Mask sensitive information
        if 'PASSWORD' in setting.upper() or 'SECRET' in setting.upper() or 'TOKEN' in setting.upper() or 'KEY' in setting.upper():
            value = '***HIDDEN***' if value != 'Not Set' else 'Not Set'
        settings[setting] = value

    return settings

def get_app_configuration():
    """Get Flask application configuration."""
    config = {}

    # Safe configuration items to display
    safe_config_keys = [
        'MAINTENANCE_MODE',
        'ALLOWED_IPS',
        'MAX_CONTENT_LENGTH',
        'SESSION_PERMANENT',
        'PERMANENT_SESSION_LIFETIME',
        'SESSION_REFRESH_EACH_REQUEST',
        'WTF_CSRF_ENABLED'
    ]

    for key in safe_config_keys:
        if key in airtanker_app.config:
            config[key] = airtanker_app.config[key]

    return config

def get_system_information():
    """Get system and application information."""
    import platform
    import sys
    from datetime import datetime

    info = {
        'Python Version': sys.version,
        'Platform': platform.platform(),
        'Flask Version': getattr(airtanker_app, '__version__', 'Unknown'),
        'Current Time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'Debug Mode': airtanker_app.debug,
        'Environment': 'Production' if os.getenv('PRODUCTION', 'false').lower() == 'true' else 'Development'
    }

    return info

@airtanker_app.route('/api/settings/toggle_maintenance', methods=['POST'])
@requires_authentication
@requires_ldap_group_authentication
def toggle_maintenance_mode():
    """API endpoint to toggle maintenance mode."""
    try:
        current_mode = airtanker_app.config.get('MAINTENANCE_MODE', False)
        new_mode = not current_mode
        airtanker_app.config['MAINTENANCE_MODE'] = new_mode

        airtanker_app.logger.info(f'Maintenance mode {"enabled" if new_mode else "disabled"} by user {session.get("name", "Unknown")}')

        return jsonify({
            'success': True,
            'maintenance_mode': new_mode,
            'message': f'Maintenance mode {"enabled" if new_mode else "disabled"}'
        })
    except Exception as e:
        airtanker_app.logger.error(f'Error toggling maintenance mode: {str(e)}')
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@airtanker_app.route('/api/settings/system_status', methods=['GET'])
@requires_authentication
def get_system_status():
    """API endpoint to get current system status."""
    try:
        from services.DatabaseService import DatabaseService

        # Test database connection
        db_service = DatabaseService()
        db_connected = db_service.connect()
        if db_connected:
            db_service.disconnect()

        # Test Redis connection
        redis_connected = False
        try:
            from main import redis_client
            redis_client.ping()
            redis_connected = True
        except:
            redis_connected = False

        status = {
            'database': 'Connected' if db_connected else 'Disconnected',
            'redis': 'Connected' if redis_connected else 'Disconnected',
            'maintenance_mode': airtanker_app.config.get('MAINTENANCE_MODE', False),
            'environment': 'Production' if os.getenv('PRODUCTION', 'false').lower() == 'true' else 'Development'
        }

        return jsonify(status)

    except Exception as e:
        airtanker_app.logger.error(f'Error getting system status: {str(e)}')
        return jsonify({
            'error': str(e)
        }), 500