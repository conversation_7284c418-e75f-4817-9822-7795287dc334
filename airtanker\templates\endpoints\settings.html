{% extends "base.html" %}

{% block styles %}
  <link rel="stylesheet" href="{{ url_for('static', filename='css/extra.css') }}" />
  <style>
    .settings-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
      padding: 20px;
    }
    
    .settings-header {
      border-bottom: 2px solid #f0f0f0;
      margin-bottom: 20px;
      padding-bottom: 10px;
    }
    
    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }
    
    .status-connected {
      background-color: #28a745;
    }
    
    .status-disconnected {
      background-color: #dc3545;
    }
    
    .status-maintenance {
      background-color: #ffc107;
    }
    
    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .setting-item:last-child {
      border-bottom: none;
    }
    
    .setting-key {
      font-weight: 600;
      color: #333;
    }
    
    .setting-value {
      color: #666;
      font-family: monospace;
      background: #f8f9fa;
      padding: 4px 8px;
      border-radius: 4px;
    }
    
    .btn-maintenance {
      transition: all 0.3s ease;
    }
    
    .system-status {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
  </style>
{% endblock %}

{% block scripts %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Load system status
      loadSystemStatus();
      
      // Auto-refresh system status every 30 seconds
      setInterval(loadSystemStatus, 30000);
      
      // Handle maintenance mode toggle
      document.getElementById('toggleMaintenance').addEventListener('click', function() {
        toggleMaintenanceMode();
      });
    });
    
    function loadSystemStatus() {
      fetch('/api/settings/system_status')
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            console.error('Error loading system status:', data.error);
            return;
          }
          
          updateStatusIndicator('db-status', data.database);
          updateStatusIndicator('redis-status', data.redis);
          updateMaintenanceStatus(data.maintenance_mode);
          
          document.getElementById('env-status').textContent = data.environment;
        })
        .catch(error => {
          console.error('Error loading system status:', error);
        });
    }
    
    function updateStatusIndicator(elementId, status) {
      const element = document.getElementById(elementId);
      const indicator = element.querySelector('.status-indicator');
      
      indicator.className = 'status-indicator';
      if (status === 'Connected') {
        indicator.classList.add('status-connected');
      } else {
        indicator.classList.add('status-disconnected');
      }
      
      element.querySelector('.status-text').textContent = status;
    }
    
    function updateMaintenanceStatus(isMaintenanceMode) {
      const element = document.getElementById('maintenance-status');
      const indicator = element.querySelector('.status-indicator');
      const button = document.getElementById('toggleMaintenance');
      
      indicator.className = 'status-indicator';
      if (isMaintenanceMode) {
        indicator.classList.add('status-maintenance');
        element.querySelector('.status-text').textContent = 'Enabled';
        button.textContent = 'Disable Maintenance Mode';
        button.className = 'btn btn-warning btn-maintenance';
      } else {
        indicator.classList.add('status-connected');
        element.querySelector('.status-text').textContent = 'Disabled';
        button.textContent = 'Enable Maintenance Mode';
        button.className = 'btn btn-outline-warning btn-maintenance';
      }
    }
    
    function toggleMaintenanceMode() {
      const button = document.getElementById('toggleMaintenance');
      button.disabled = true;
      button.textContent = 'Processing...';

      fetch('/api/settings/toggle_maintenance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      .then(response => {
        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          return response.json();
        } else {
          // If we get HTML instead of JSON, it means maintenance mode blocked the request
          throw new Error('Maintenance mode is blocking the request. Use /admin/settings to disable it.');
        }
      })
      .then(data => {
        if (data.success) {
          updateMaintenanceStatus(data.maintenance_mode);
          showAlert(data.message, 'success');

          // If maintenance mode was enabled, show admin URL info
          if (data.maintenance_mode) {
            showAlert('Maintenance mode enabled. To disable it, use: /admin/settings', 'info');
          }
        } else {
          showAlert('Error: ' + data.error, 'danger');
        }
      })
      .catch(error => {
        console.error('Error toggling maintenance mode:', error);
        showAlert(error.message || 'Error toggling maintenance mode', 'danger');
      })
      .finally(() => {
        button.disabled = false;
      });
    }
    
    function showAlert(message, type) {
      const alertDiv = document.createElement('div');
      alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
      alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;
      
      const container = document.querySelector('.container');
      container.insertBefore(alertDiv, container.firstChild);
      
      // Auto-dismiss after 5 seconds
      setTimeout(() => {
        alertDiv.remove();
      }, 5000);
    }
  </script>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-12">
      <h1><i class="ri-settings-3-line"></i> Admin Settings</h1>
      {% if bypass_auth %}
        <div class="alert alert-warning" role="alert">
          <i class="ri-shield-line"></i>
          <strong>Emergency Access Mode:</strong> You are accessing this page via URL parameter bypass.
          This mode works even during maintenance mode and without authentication.
        </div>
      {% else %}
        <p class="text-muted">Manage application configuration and monitor system status</p>
      {% endif %}
    </div>
  </div>

  <!-- Flash Messages -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <div class="row">
    <!-- System Status -->
    <div class="col-md-6">
      <div class="settings-card">
        <div class="settings-header">
          <h3><i class="ri-pulse-line"></i> System Status</h3>
        </div>
        
        <div class="system-status" id="db-status">
          <span class="status-indicator status-disconnected"></span>
          <strong>Database:</strong>
          <span class="status-text ms-2">Checking...</span>
        </div>
        
        <div class="system-status" id="redis-status">
          <span class="status-indicator status-disconnected"></span>
          <strong>Redis:</strong>
          <span class="status-text ms-2">Checking...</span>
        </div>
        
        <div class="system-status" id="maintenance-status">
          <span class="status-indicator status-connected"></span>
          <strong>Maintenance Mode:</strong>
          <span class="status-text ms-2">Checking...</span>
        </div>
        
        <div class="system-status">
          <span class="status-indicator status-connected"></span>
          <strong>Environment:</strong>
          <span id="env-status" class="ms-2">{{ system_info.get('Environment', 'Unknown') }}</span>
        </div>
        
        <div class="mt-3">
          <button id="toggleMaintenance" class="btn btn-outline-warning btn-maintenance">
            Toggle Maintenance Mode
          </button>
          <div class="mt-2">
            <small class="text-muted">
              <i class="ri-information-line"></i>
              <strong>Access Methods:</strong><br>
              • Normal: <code>/admin/settings</code> (requires authentication)<br>
              • Emergency: <code>/admin/settings?admin=1</code> or <code>/admin/settings?debug=1</code> (bypasses authentication)<br>
              • API: <code>POST /emergency/disable_maintenance</code> (requires emergency key)
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- System Information -->
    <div class="col-md-6">
      <div class="settings-card">
        <div class="settings-header">
          <h3><i class="ri-information-line"></i> System Information</h3>
        </div>
        
        {% for key, value in system_info.items() %}
          <div class="setting-item">
            <span class="setting-key">{{ key }}:</span>
            <span class="setting-value">{{ value }}</span>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Environment Settings -->
    <div class="col-md-6">
      <div class="settings-card">
        <div class="settings-header">
          <h3><i class="ri-file-settings-line"></i> Environment Settings</h3>
        </div>
        
        {% for key, value in settings.items() %}
          <div class="setting-item">
            <span class="setting-key">{{ key }}:</span>
            <span class="setting-value">{{ value }}</span>
          </div>
        {% endfor %}
      </div>
    </div>

    <!-- Application Configuration -->
    <div class="col-md-6">
      <div class="settings-card">
        <div class="settings-header">
          <h3><i class="ri-flask-line"></i> Application Configuration</h3>
        </div>
        
        {% for key, value in app_config.items() %}
          <div class="setting-item">
            <span class="setting-key">{{ key }}:</span>
            <span class="setting-value">{{ value }}</span>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="row">
    <div class="col-12">
      <div class="settings-card">
        <div class="settings-header">
          <h3><i class="ri-tools-line"></i> Quick Actions</h3>
        </div>
        
        <div class="row">
          <div class="col-md-3">
            <a href="/logs" class="btn btn-outline-primary w-100 mb-2">
              <i class="ri-file-text-line"></i> View Logs
            </a>
          </div>
          <div class="col-md-3">
            <a href="/dashboard" class="btn btn-outline-info w-100 mb-2">
              <i class="ri-dashboard-line"></i> Dashboard
            </a>
          </div>
          <div class="col-md-3">
            <a href="/exports" class="btn btn-outline-success w-100 mb-2">
              <i class="ri-download-line"></i> Exports
            </a>
          </div>
          <div class="col-md-3">
            {% if bypass_auth %}
              <a href="/admin/settings?admin=1" class="btn btn-outline-warning w-100 mb-2">
                <i class="ri-refresh-line"></i> Reload Page
              </a>
            {% else %}
              <a href="/" class="btn btn-outline-secondary w-100 mb-2">
                <i class="ri-home-line"></i> Home
              </a>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
