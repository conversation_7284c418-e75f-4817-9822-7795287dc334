<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Maintenance Disable</title>
    <style>
        body {
            background-color: #f4f4f4;
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            text-align: center;
            color: #333;
        }
        .container {
            background: #fff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
        }
        h1 {
            font-size: 2rem;
            margin-bottom: 20px;
            color: #dc3545;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        button {
            background-color: #dc3545;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background-color: #c82333;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: none;
        }
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info-box {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: left;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Emergency Maintenance Disable</h1>
        
        <div class="info-box">
            <strong>Warning:</strong> This is an emergency endpoint to disable maintenance mode when you're locked out. 
            Only use this if you cannot access the normal settings page.
        </div>
        
        <div id="alert" class="alert"></div>
        
        <form id="emergencyForm">
            <div class="form-group">
                <label for="emergencyKey">Emergency Key:</label>
                <input type="password" id="emergencyKey" name="emergencyKey" required 
                       placeholder="Enter the emergency maintenance key">
            </div>
            
            <button type="submit" id="submitBtn">Disable Maintenance Mode</button>
        </form>
        
        <div style="margin-top: 30px; font-size: 0.8rem; color: #666;">
            <p>After successfully disabling maintenance mode, you can access the normal login page.</p>
            <p><a href="/login">Go to Login Page</a> | <a href="/settings">Go to Settings</a></p>
        </div>
    </div>

    <script>
        document.getElementById('emergencyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const alert = document.getElementById('alert');
            const emergencyKey = document.getElementById('emergencyKey').value;
            
            if (!emergencyKey) {
                showAlert('Please enter the emergency key', 'danger');
                return;
            }
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Processing...';
            
            fetch('/emergency/disable_maintenance', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    emergency_key: emergencyKey
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('✅ ' + data.message + ' You can now access the application normally.', 'success');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 3000);
                } else {
                    showAlert('❌ Error: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('❌ Network error occurred. Please try again.', 'danger');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Disable Maintenance Mode';
            });
        });
        
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.className = 'alert alert-' + type;
            alert.textContent = message;
            alert.style.display = 'block';
        }
    </script>
</body>
</html>
